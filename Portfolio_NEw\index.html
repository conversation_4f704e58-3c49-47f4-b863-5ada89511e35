<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Portfolio of <PERSON><PERSON>, Reader, Builder" />
    <title><PERSON><PERSON> | Developer • Reader • Builder</title>
    <link rel="icon" href="assets/images/tab.png" type="image/png">
    <link rel="stylesheet" href="css/style.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Product+Sans:wght@400;700&display=swap" rel="stylesheet">
    <script defer src="js/script.js"></script>

</head>

<body>
    <button id="unmuteButton">Enable Music</button>
    <audio id="bgMusic" loop>
        <source src="assets/background-music.mp3" type="audio/mpeg">
    </audio>
    <video autoplay muted loop id="bg-video">
        <source src="assets/background-video.mp4" type="video/mp4">
        Your browser does not support HTML5 video.
    </video>
    <div id="bg-overlay"></div>
    <div class="main-container">
        <!-- LEFT CONTENT -->
        <main class="left-content">
            <!-- HOME -->
            <section id="home" class="section">
                <h1>Hi, I'm
                    <span class="typewriter" id="animated-name">
                        <span class="typewriter-cursor"></span>
                        Ashwin Patel
                    </span>
                </h1>
                <p class="subtitle">Builder• Blogger • Developer • Reader • Dreamer • Traveller 
                </p>
            </section>

            <!-- ABOUT -->
            <section id="about" class="section">
                <h2>About Me</h2>
                <p>A tech enthusiast with a cricket bat in one hand and curiosity in the other. When I’m not coding or leading projects, I’m busy decoding the world — from startups to space, history to hype.</p><br>
<p>I blend logic with big-picture thinking to build solutions that are both smart and impactful (with some help from AI, of course).</p>

      <section id="experience" class="section">
    <h2>Experience</h2>
    <div class="experience-timeline">
        
        <div class="experience-item">
            <div class="experience-header">
                <h3>Chegg India - Subject Matter Expert</h3>
                <span class="experience-date">October 2022 – Present</span>
            </div>
            <ul class="experience-contributions">
                <li>Delivered 500+ high-quality solutions to global students with an average rating of 4.7/5.0.</li>
                <li>Maintained a 98% accuracy rate across 100+ advanced computer science topics.</li>
                <li>Consistently responded quickly, ensuring user satisfaction and retention.</li>
            </ul>
            <div class="skills-acquired">
                <h4>Skills Acquired:</h4>
                <ul>
                    <li>Problem Solving</li>
                    <li>Computer Science Concepts</li>
                    <li>Time Management</li>
                    <li>Online Tutoring</li>
                    <li>Technical Communication</li>
                </ul>
            </div>
        </div>

        <div class="experience-item">
            <div class="experience-header">
                <h3>Kliquestart - Team Lead</h3>
                <span class="experience-date">Nov 2022 – May 2024</span>
            </div>
            <ul class="experience-contributions">
                <li>Led a team of 5 to build an authentic community for student entrepreneurs.</li>
                <li>Drove a 30% increase in student participation across colleges in India.</li>
                <li>Launched 2 student-led startups through targeted mentorship and workshops.</li>
                <li>Boosted engagement through strategic content and networking opportunities.</li>
            </ul>
            <div class="skills-acquired">
                <h4>Skills Acquired:</h4>
                <ul>
                    <li>Team Leadership</li>
                    <li>Startup Mentorship</li>
                    <li>Content Strategy</li>
                    <li>Community Building</li>
                    <li>Public Speaking</li>
                </ul>
            </div>
        </div>

        <div class="experience-item">
            <div class="experience-header">
                <h3>Entrepreneurship Development Cell, BV(DU)COEP - Senior Associate</h3>
                <span class="experience-date">June 2022 – October 2022</span>
            </div>
            <ul class="experience-contributions">
                <li>Promoted entrepreneurship across the university, engaging over 500 students and faculty.</li>
                <li>Led cross-functional teams to organize events that boosted innovation and self-dependency.</li>
                <li>Contributed to a 20% rise in student-led initiatives and ventures.</li>
            </ul>
            <div class="skills-acquired">
                <h4>Skills Acquired:</h4>
                <ul>
                    <li>Event Planning</li>
                    <li>Cross-functional Collaboration</li>
                    <li>Innovation Strategy</li>
                    <li>Leadership</li>
                    <li>Community Engagement</li>
                </ul>
            </div>
        </div>

    </div>
</section>


            <section id="Projects" class="section">
                <h2>Projects</h2>
                <p>My projects are a reflection of my passion for innovation and problem-solving.</p>

                <!-- This is the container that your JavaScript looks for -->
                <div id="achievements-container">
                    <!-- JavaScript will populate this with timeline content -->
                </div>
            </section>

           <!-- RESUME -->
<section id="resume" class="section">
    <h2>Resume</h2>
    <p>
        <a href="assets/resume.pdf" target="_blank" rel="noopener noreferrer" class="btn">View Resume</a>
    </p>

    <div class="resume-skills">
        <h3>Skills</h3>
        <ul class="resume-skills-list">
            <li><strong>Programming Languages:</strong> JavaScript, TypeScript, Java, Python</li>
            <li><strong>Technologies/Frameworks:</strong> React.js, Next.js, Redux Toolkit, Node.js, Express.js, Tailwind CSS, Bootstrap, HTML, CSS</li>
            <li><strong>Databases:</strong> MongoDB, PostgreSQL, MySQL, Prisma</li>
            <li><strong>DevOps/Tools:</strong> AWS (EC2, S3), Docker, Cloudflare, Git, GitHub, Bash, Linux, CI/CD Pipelines, Vite, Monorepos</li>
            <li><strong>Soft Skills:</strong> Leadership, Team Management, Public Speaking, Strategic Planning, Content Writing, Event Management, Startup Mentorship, Communication</li>
        </ul>
    </div>
</section>


            <!-- THREADS -->
            <section id="threads" class="section">
                <h2>Threads (Book Quotes)</h2>
                <div id="threads-container">
                    <!-- JS will populate book dropdowns + quotes -->
                    <p class="placeholder">My favorite book quotes will appear here soon...</p>
                </div>
            </section>

            <!-- CONTACT -->
            <section id="contact" class="section">
                <h2>Get In Touch</h2>

                <form id="contact-form">
                    <!-- Remove FormSubmit action - we'll handle it via JavaScript -->
                    <input type="text" name="name" placeholder="Your Name" required>
                    <input type="email" name="email" placeholder="Your Email" required>
                    <textarea name="message" placeholder="Your Message" required></textarea>
                    <button type="submit" class="btn">Send Message</button>
                </form>

                <!-- Hidden thank-you section (already in your HTML) -->
                <div id="thank-you-message" style="display: none;">
                    <h3>Message Received! 🎉</h3>
                    <p>Thanks for reaching out. I'll get back to you soon.</p>
                    <br>
                    <button onclick="location.reload()" class="btn">Send Another</button>
                </div>

                <div class="social-links">
    <a href="https://linkedin.com/in/ashwin-patel-936964228/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
        LinkedIn
    </a>
    <a href="https://github.com/Ashwinpatel7" target="_blank" rel="noopener noreferrer" aria-label="GitHub">
        GitHub
    </a>
    <a href="https://instagram.com/yourhandle" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
        Instagram
    </a>
    <a href="https://x.com/yourhandle" target="_blank" rel="noopener noreferrer" aria-label="Twitter (X)">
        Twitter
    </a>
    <a href="https://www.reddit.com/user/yourusername" target="_blank" rel="noopener noreferrer" aria-label="Reddit">
        Reddit
    </a>
    <a href="https://medium.com/@ashwinpatel" target="_blank" rel="noopener noreferrer" aria-label="Medium">
        Medium
    </a>
    <a href="https://ashwinpatel.notion.site" target="_blank" rel="noopener noreferrer" aria-label="Notion">
        Notion
    </a>
</div>

            </section>
        </main>

        <!-- RIGHT SIDE PHOTO -->
        <aside class="right-photo" aria-label="Portrait of Ashwin Patel">
            <img src="assets/images/me.jpeg" alt="Portrait of Ashwin Patel" loading="eager" />
        </aside>
    </div>

    <!-- FOOTER -->
    <footer>
        <p>Email Me at <b><EMAIL></b> </p>
        <p>
        <h3 style="display: inline-block;">&copy;</h3> <span id="current-year">2025</span> Ashwin Patel. Made with ☕ and
        ❤️</p>
    </footer>
    <script>
        const unmuteButton = document.getElementById('unmuteButton');
        const bgMusic = document.getElementById('bgMusic');

        unmuteButton.addEventListener('click', () => {
            bgMusic.play();
            unmuteButton.style.display = 'none'; // Hide after click
        });
    </script>
</body>

</html>