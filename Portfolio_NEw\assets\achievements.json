[{"title": "Graph-e-thon 2.0 - Graphic Era Deemed to be University", "date": "2025-04-23", "description": "Project: Built an AI-enabled Course Generator with dedicated Modules, Quizzes, Assignments, and also a Video-Generation tool (USP).\n\nRanked among the Top 10 teams, received a full company establishment fees reimbursement and incubation amount of Rs. 25,000/-.", "proofLink": "https://drive.google.com/file/d/1TKzst75OXFkH0E_6KcVry9SheV3Pj4bj/", "rating": 3, "category": "<PERSON><PERSON><PERSON><PERSON>", "icon": "🏆"}, {"title": "Finance Among Us - Plaksha University", "date": "2025-03-31", "description": "1st Position\n\nBusiness Simulation Competition with Nine Rounds, competing for the maximum share of the company among DIIs (us), FIIs, Promoters and Retail Investors.", "proofLink": "https://drive.google.com/file/d/1-N0FEP8Rr-AtGcym8fspc3RVCamrZP7X/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Reverse 8.0 AIT Finalist", "date": "2025-03-31", "description": "Among Top 25 poets across Pune City, qualified for the finale with my poem: \"The Three Deaths of Me\".", "proofLink": "https://drive.google.com/file/d/13Ze-dwj13C1TPFWWvsMUTmXRg7r2ADmS/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Smart India Hackathon Winner", "date": "2024-12-12", "description": "Ministy: Ministry of AYUSH\nNodal Centre: IIT Tirupati\nProject: To build a solution to rank various departments of an institute and analyse various measures, with visualisations and also provide role-based access.\n\nhttps://pi-ex.vercel.app/", "proofLink": "https://drive.google.com/file/d/1GudqYxzs0USNsaKc8Jgzs_f_9JOV78qM/", "rating": 5, "category": "<PERSON><PERSON><PERSON><PERSON>", "icon": "🏆"}, {"title": "TechElevate X AIT", "date": "2023-09-20", "description": "1st Position\n\nPitched an idea of an application that would be the single-platform for all events organised by various clubs, with role-based access.", "proofLink": "https://drive.google.com/file/d/18BDPZO9zH10xPMcO2K2IXv9uzqxdDwsP/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Reliance Foundation Scholarship", "date": "2023-05-24", "description": "Among top 5000 scholars, receiving a yearly amount of Rs. 50,000/- for four years.", "proofLink": "https://drive.google.com/file/d/1pCXSB950huLHotiP-lfCjnOssybwPyTX/", "rating": 5, "category": "Scholarship", "icon": "🎓"}, {"title": "Startup Saga", "date": "2023-04-16", "description": "3rd Position - Shark Tank AIT\n5th Position - No Code Startup\n\nFor Shark Tank AIT - We pitched an idea for a theme park of defence experiences.\n\nFor No Code Startup - We built an app that would calculate the energy efficiency of a battery cell.", "proofLink": "https://drive.google.com/file/d/1WuQwQXMPlmWmINb2JSOfqkP9DzZeWt3D/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "<PERSON> Quiz", "date": "2023-03-18", "description": "2nd Position\n\n\"PRACTICE is the best of all instructors! Great successful people materialized their THOUGHTS and steadily built their EMPIRE. The person who serves it the best, PROFITS the most! Screw YOLO! You live every single PLANCK LENGTH OF TIME of your LIFE!!\"", "proofLink": "https://drive.google.com/file/d/1adqrFPiE670Jdp1r7Pm47EGjTGt4YZQs/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Placement Apti Final Round - Technical Aak<PERSON>i", "date": "2023-02-17", "description": "1st Position\n\nIt involved two rounds of aptitude and one round of personal interview, competing across all students of the college.", "proofLink": "https://drive.google.com/file/d/143Uamy2YicLdFQVOC3kq2eJwUPKiOFNh/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Impression 3.0", "date": "2022-11-07", "description": "1st Position\n\nResearched about the Markets of Ola and Uber and provided necessary solutions for the problems faced by the firms.", "proofLink": "https://drive.google.com/file/d/103vDAs7TJBsa_GIPRl_Q7JQlzxP10aIC/", "rating": 3, "category": "Competition", "icon": "🎯"}, {"title": "Joint Entrance Examination - Advanced (AIR: 17472)", "date": "2022-09-11", "description": "Considered to be one of the world's Toughest Exams.", "proofLink": "https://drive.google.com/file/d/1Ea5HK2BglDH9GiW7rrC--Ta9pxnJ75ht/", "rating": 4, "category": "Entrance Exam", "icon": "✒️"}, {"title": "Joint Entrance Examination - Mains (AIR 31677)", "date": "2022-08-07", "description": "Total: 96.4859749%\n\nPhysics: 93.1922552%\nChemistry: 90.8526747%\nMathematics: 99.1498076%", "proofLink": "https://drive.google.com/file/d/11XnlDNFMt82pa4dZQ2ebUolqQ_m7Bo7P/", "rating": 4, "category": "ENTRANCE EXAM", "icon": "✒️"}, {"title": "Technical Entry Scheme - 46th Course (AIR 58)", "date": "2022-01-13", "description": "SSB RECOMMENDED for the INDIAN ARMY from 19 Services Selection Board Allahabad.", "proofLink": "https://drive.google.com/file/d/1Lw9rb_hoLevhyWDW5vClkPFWYkeZOW2Q/", "rating": 5, "category": "ENTRANCE EXAM", "icon": "🪖"}, {"title": "National Defence Academy - 147th Course (AIR 52)", "date": "2021-10-15", "description": "SSB RECOMMENDED for the INDIAN AIR FORCE (FLYING BRANCH - CPSS Passed) from 04 Air Force Selection Board Varanasi.\n\nTotal: 893 Marks\n\nWRITTEN: 512/900\nSSB: 384/900", "proofLink": "https://drive.google.com/file/d/1bj9Fi2cjGmRFB6AJQTnM2Z2R8571tQTS/", "rating": 5, "category": "ENTRANCE EXAM", "icon": "✈️"}, {"title": "Scholastic Aptitude Test (SAT)", "date": "2021-10-02", "description": "Total: 1350/1600\n\nMath: 780/800\nEnglish: 570/800", "proofLink": "https://drive.google.com/file/d/1W5NT7gaSPEXtMyUdmCW0KFAls3pOhkA5/", "rating": 4, "category": "ENTRANCE EXAM", "icon": "✒️"}, {"title": "Test Of English as a Foreign Language (TOEFL)", "date": "2021-09-05", "description": "Total: 93/120\n\nListening: 25/30\nReading: 24/30\nSpeaking: 23/30\nWriting: 21/30", "proofLink": "https://drive.google.com/file/d/1UeJGJjcsjJMSeWoOsJkodwfgHioUCmHI/", "rating": 4, "category": "ENTRANCE EXAM", "icon": "✒️"}, {"title": "CLASS 12 ISC BOARD Examinations", "date": "2021-07-24", "description": "Average Marks: 85%\n\nMaths: 89/100\nComputer Science: 93/100\nPhysics: 82/100\nChemistry: 74/100\nEnglish: 88/100", "proofLink": "https://drive.google.com/file/d/1W1EcOi47Ab2KxGthjHfwjdsSToLfjOi_/", "rating": 4, "category": "Test Score", "icon": "🎓"}, {"title": "Pearson Undergraduate Entrance Exam", "date": "2021-07-23", "description": "Total: 780/900\n\nQuantitative Reasoning: 300/300\nAbstract Reasoning: 257/300\nVerbal Reasoning: 223/300", "proofLink": "https://drive.google.com/file/d/1he68alvtF1dCLMzd2UhSHTQi7mgZI92Z/", "rating": 4, "category": "ENTRANCE EXAM", "icon": "✒️"}, {"title": "National Science Talent Search Examination (AIR 75)", "date": "2021-01-31", "description": "Critical Thinking: AIR 1\nMathematics: AIR 97\nChemistry: AIR 103\nPhysics: AIR 121", "proofLink": "https://drive.google.com/file/d/1kNOrCoXyN57c8frZc1Uay2phjazs6lef/", "rating": 4, "category": "Test Score", "icon": "🎓"}, {"title": "CLASS 10 ICSE BOARD Examinations", "date": "2019-05-07", "description": "Average Marks: 93%\n\nMaths: 100/100\nComputer Applications: 95/100\nScience: 84/100\nSocial Studies: 90/100\nEnglish: 84/100\nHindi: 94/100", "proofLink": "https://drive.google.com/file/d/1HUimJUEjh9209rTV1WkqkQsVDI4KoSlk/", "rating": 4, "category": "Test Score", "icon": "🎓"}]