# 🚀 <PERSON><PERSON> Patel - Portfo<PERSON>

> **Developer • Reader • Builder • Dreamer**

A modern, interactive portfolio showcasing my journey as a full-stack developer, featuring dynamic projects, book collections, and professional experience.

## ✨ Features

- 🎨 **Interactive Design** - Smooth animations and responsive layout
- 💼 **Dynamic Projects** - Live demos and GitHub links for all projects
- 📚 **Book Collection** - Personal reading list with quotes and Amazon links
- 🎵 **Background Music** - Optional ambient music for enhanced experience
- 📱 **Mobile Responsive** - Optimized for all device sizes
- ⚡ **Fast Loading** - Optimized assets and efficient code

## 🛠️ Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with modern animations
- **Data**: JSON-based content management
- **Deployment**: Vercel/Netlify ready

## 🎯 Sections

### 🏠 Home
- Animated typewriter name effect
- Professional subtitle with role highlights

### 👨‍💻 About
- Personal introduction and philosophy
- Cricket enthusiast meets tech innovator

### 💼 Experience
- **Chegg India** - Subject Matter Expert (2022-Present)
- **<PERSON><PERSON><PERSON>t** - Team Lead (2022-2024)
- **Entrepreneurship Development Cell** - Senior Associate (2022)

### 🚀 Projects
Dynamic showcase of Next.js applications:
- **Vendor Management Tool** - [Live Demo](https://vendor-app-psi.vercel.app/)
- **Artist Management App** - [Live Demo](https://artistly-nu-lovat.vercel.app/)
- **Final Fusion Translator** - [Live Demo](https://final-fusion-jcaj.vercel.app/)
- **Pizza Delivery App** - [Live Demo](https://pizza-flow-khaki.vercel.app/)
- Course Selling Platform
- Blogging Website
- Payment App (Paytm Clone)

### 📖 Books & Quotes
Personal reading collection featuring:
- The Alchemist by Paulo Coelho
- Harry Potter Series by J.K. Rowling
- Chetan Bhagat Collection
- Robin Sharma Books
- Atomic Habits by James Clear
- And more...

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Ashwinpatel7/portfolio.git
   cd portfolio
   ```

2. **Open in browser**
   ```bash
   # Simply open index.html in your browser
   # Or use a local server
   python -m http.server 8000
   # or
   npx serve .
   ```

3. **Customize**
   - Update `assets/projects.json` for your projects
   - Update `assets/books.json` for your reading list
   - Replace images in `assets/images/`
   - Modify content in `index.html`

## 📁 Project Structure

```
portfolio/
├── 📄 index.html          # Main HTML file
├── 📁 css/
│   ├── style.css          # Main stylesheet
│   └── s2.css            # Additional styles
├── 📁 js/
│   ├── script.js         # Main JavaScript
│   └── v2.js             # Additional scripts
├── 📁 assets/
│   ├── 📄 projects.json   # Projects data
│   ├── 📄 books.json      # Books data
│   ├── 📄 resume.pdf      # Resume file
│   ├── 🎵 background-music.mp3
│   ├── 🎬 background-video.mp4
│   └── 📁 images/
│       ├── 📁 book-covers/
│       ├── me.jpeg        # Profile photo
│       └── tab.png        # Favicon
└── 📄 README.md
```

## 🎨 Customization

### Adding New Projects
Edit `assets/projects.json`:
```json
{
  "title": "Your Project Name",
  "description": "Project description",
  "technologies": ["Next.js", "React", "JavaScript"],
  "liveLink": "https://your-demo.com",
  "githubLink": "https://github.com/username/repo",
  "category": "Web Application",
  "icon": "🚀"
}
```

### Adding New Books
Edit `assets/books.json`:
```json
{
  "bookName": "Book Title",
  "author": "Author Name",
  "genre": "Genre",
  "publishYear": "Year",
  "dateOfCompletion": "YYYY-MM-DD",
  "coverImage": "./assets/images/book-covers/BookCover.jpg",
  "amazonLink": "Amazon URL",
  "quotes": ["Quote 1", "Quote 2", "Quote 3"]
}
```

## 🌟 Key Features Explained

- **Typewriter Animation**: Custom JavaScript animation for name display
- **Dynamic Content Loading**: JSON-based content management
- **Responsive Design**: Mobile-first approach with CSS Grid/Flexbox
- **Interactive Elements**: Hover effects, smooth scrolling, expandable cards
- **Performance Optimized**: Lazy loading, optimized images, minimal dependencies

## 📱 Browser Support

- ✅ Chrome (Latest)
- ✅ Firefox (Latest)
- ✅ Safari (Latest)
- ✅ Edge (Latest)
- ✅ Mobile browsers

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

**Ashwin Patel**
- 📧 Email: <EMAIL>
- 💼 LinkedIn: [ashwin-patel-936964228](https://linkedin.com/in/ashwin-patel-936964228/)
- 🐙 GitHub: [Ashwinpatel7](https://github.com/Ashwinpatel7)
- 🌐 Portfolio: [Live Demo](https://your-portfolio-url.com)

---

<div align="center">
  <p>Made with ☕ and ❤️ by Ashwin Patel</p>
  <p>⭐ Star this repo if you found it helpful!</p>
</div>