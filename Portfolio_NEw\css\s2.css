/* ========== GLOBAL & VARIABLES ========== */
:root {
  --primary: #1a73e8;
  --primary-dark: #0f5ddc;
  --bg-dark: #121212;
  --bg-light: #1f1f1f;
  --text-light: #e0e0e0;
  --text-lighter: #ffffff;
  --text-gray: #888;
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

html {
  font-size: 16px;
}

#bg-video {
  position: fixed;
  top: 0;
  left: 0;
  min-width: 100vw;
  min-height: 100vh;
  z-index: -2;
  object-fit: cover;
}

#bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  /* 50% dark overlay */
  z-index: -1;
}

body {
  background-color: var(--bg-dark);
  color: var(--text-light);
  font-family: 'Product Sans', 'Segoe UI', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* ========== TYPOGRAPHY ========== */
h1,
h2,
h3 {
  line-height: 1.2;
  font-weight: 600;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: var(--text-lighter);
  position: relative;
}

h2::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 60px;
  height: 3px;
  background-color: var(--primary);
  border-radius: 3px;
}

.subtitle {
  font-size: 1.1rem;
  color: var(--text-gray);
  margin-bottom: 2rem;
}

.highlight {
  color: var(--primary);
}

/* ========== LAYOUT COMPONENTS ========== */
.main-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 120px auto 40px;
  padding: 0 20px;
  gap: 50px;
}

.left-content {
  flex: 2;
}

.right-photo {
  flex: 1;
  position: sticky;
  top: 100px;
  display: flex;
  justify-content: center;
}

.right-photo img {
  width: 240px;
  height: 240px;
  object-fit: cover;
  border-radius: 50%;
  border: 4px solid var(--primary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  transition: var(--transition);
}

.right-photo img:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.7);
}

.section {
  padding: 80px 20px;
  max-width: 900px;
  margin: auto;
  animation: fadeIn 0.6s ease-out forwards;
}

.section:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* ========== NAVIGATION ========== */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: var(--bg-light);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.6);
  padding: 15px 0;
  z-index: 1000;
  transition: var(--transition);
}

.nav-links {
  display: flex;
  justify-content: center;
  list-style: none;
  gap: 25px;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-light);
  font-weight: 500;
  padding: 5px 10px;
  transition: var(--transition);
  position: relative;
}

.nav-links a:hover {
  color: var(--primary);
}

.nav-links a::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -2px;
  width: 0;
  height: 2px;
  background-color: var(--primary);
  transition: var(--transition);
  transform: translateX(-50%);
}

.nav-links a:hover::after {
  width: 100%;
}

/* ========== UI COMPONENTS ========== */
.btn {
  display: inline-block;
  background-color: var(--primary);
  color: #fff;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 500;
  border: none;
  cursor: pointer;
  text-align: center;
}

.btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.btn[disabled] {
  background-color: #555;
  cursor: not-allowed;
}

/* Cards */
.project-card {
  background-color: var(--bg-light);
  padding: 20px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.project-card h3 {
  color: var(--primary);
  margin-bottom: 10px;
}

.icon {
  width: 18px;
  height: 18px;
  fill: currentColor;
}

/* Project Buttons */
.project-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.project-buttons .btn {
  flex: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  gap: 8px;
  min-height: 44px;
}

.github-btn {
  background-color: #333;
  color: #fff;
  flex: 1;
}

.github-btn:hover {
  background-color: #24292e;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(51, 51, 51, 0.3);
}

.project-btn {
  background-color: var(--primary);
  color: #fff;
  flex: 1;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 500px;
  margin-top: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

label {
  color: var(--text-lighter);
  font-weight: 500;
}

input,
textarea {
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  background-color: var(--bg-light);
  color: var(--text-light);
  resize: none;
  transition: var(--transition);
}

input:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* ========== CONTENT SECTIONS ========== */
/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

/* Resume Skills */
.resume-skills {
  margin-top: 30px;
  background-color: var(--bg-light);
  padding: 20px;
  border-radius: var(--border-radius);
}

.resume-skills h3 {
  color: var(--primary);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.resume-skills-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 12px;
}

.resume-skills-list li {
  display: flex;
}

.resume-skills-list strong {
  color: var(--text-lighter);
  flex-shrink: 0;
  min-width: 210px;
}

/* ========== ENHANCED THREADS (QUOTES) STYLING ========== */
#threads-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.book-card {
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 280px;
  /* Fixed height for consistency */
  display: flex;
  flex-direction: column;
}

.book-card:hover {
  border-color: var(--primary);
  box-shadow: 0 4px 15px rgba(26, 115, 232, 0.2);
}

.book-preview {
  display: flex;
  align-items: stretch;
  padding: 20px;
  cursor: pointer;
  transition: var(--transition);
  gap: 20px;
  height: 100%;
  /* Take full height of card */
  min-height: 240px;
  /* Consistent minimum height */
}

.book-preview:hover {
  background-color: rgba(26, 115, 232, 0.05);
}

.book-cover {
  width: 140px;
  /* Slightly smaller to fit better */
  height: 200px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.book-image {
  width: 100%;
  height: auto;
  max-height: 200px;
  /* Consistent image height */
  object-fit: fill;
  border-radius: calc(var(--border-radius) - 1px);
}

.book-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 10px;
  overflow: hidden;
  /* Prevent content overflow */
}

.book-header {
  margin-bottom: 12px;
}

.book-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 6px;
  line-height: 1.2;
  /* Limit title to 2 lines */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  font-size: 0.9rem;
  color: var(--text-gray);
  margin: 0;
}

.featured-quote {
  flex: 1;
  font-style: italic;
  color: var(--text-light);
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 12px 0;
  padding: 12px;
  background-color: rgba(26, 115, 232, 0.05);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary);
  /* Limit quote to 4 lines */
  display: -webkit-box;

  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-meta {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 80px;
}

.meta-icon {
  font-size: 1rem;
  margin-bottom: 3px;
}

.meta-label {
  font-size: 0.7rem;
  color: var(--text-gray);
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.meta-value {
  font-size: 0.8rem;
  color: var(--text-light);
  font-weight: 500;
  /* Prevent text wrapping */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  font-size: 1.2rem;
  transition: var(--transition);
  flex-shrink: 0;
}

.book-card.expanded .expand-indicator {
  transform: rotate(120deg);
  background-color: var(--primary-dark);
}

.quotes-container {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.6s ease-out, padding 0.4s ease-out;
  padding: 0 20px;
}

.book-card.expanded {
  height: auto;
  /* Allow expansion when opened */
}

.book-card.expanded .quotes-container {
  max-height: none;
  height: auto;
  overflow: visible;
  padding: 0 20px 20px 20px;
}

.quotes-list {
  display: grid;
  gap: 15px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.quote-item {
  background-color: rgba(26, 115, 232, 0.05);
  padding: 15px;
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary);
  font-style: italic;
  line-height: 1.6;
  position: relative;
  transition: var(--transition);
  display: flex;
  gap: 10px;
}

.quote-item:hover {
  background-color: rgba(26, 115, 232, 0.1);
  transform: translateX(5px);
}

.quote-number {
  color: var(--primary);
  font-weight: 600;
  flex-shrink: 0;
}

.quote-text {
  color: var(--text-light);
  white-space: pre-line;
  flex: 1;
}

/* Loading and error states */
.loading-quotes {
  text-align: center;
  padding: 20px;
  color: var(--text-gray);
}

.error-message {
  color: #ff4444;
  text-align: center;
  padding: 20px;
  background-color: rgba(255, 68, 68, 0.1);
  border-radius: var(--border-radius);
  margin: 10px 0;
}

/* Responsive design */
@media (max-width: 1200px) {
  #threads-container {
    grid-template-columns: 1fr;
    /* Single column on smaller screens */
  }
}

@media (max-width: 768px) {
  .book-preview {
    flex-direction: column;
    min-height: auto;
    height: auto;
  }

  .book-card {
    height: auto;
    /* Allow flexible height on mobile */
  }

  .book-cover {
    width: 100px;
    align-self: center;
  }

  .book-meta {
    flex-direction: column;
    gap: 8px;
  }

  .meta-item {
    flex-direction: row;
    align-items: center;
    gap: 8px;
    min-width: auto;
  }
}

/* ========== MESSAGES & FEEDBACK ========== */
.success-message,
#thank-you-message {
  color: #34A853;
  text-align: center;
  animation: fadeIn 0.5s;
}

/* ========== ANIMATIONS ========== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

/* ========== TYPEWRITER EFFECT ========== */
.typewriter {
  position: absolute;
}

.typewriter-cursor {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 1.2em;
  background: var(--primary);
  animation: blink 3s infinite;
}

.typewriter-cursor.done {
  animation: none;
}

#animated-name {
  display: inline-block;
  padding-left: 4px;
  position: relative;
}

/* Letter Colors */
#animated-name span.k {
  color: #4285F4;
}

/* K - blue */
#animated-name span.a {
  color: #EA4335;
}

/* a - red */
#animated-name span.u {
  color: #FBBC05;
}

/* u - yellow */
#animated-name span.s {
  color: #4285F4;
}

/* s - blue */
#animated-name span.h {
  color: #34A853;
}

/* h - green */
#animated-name span.l {
  color: #4285F4;
}

/* l - blue */
#animated-name span.v {
  color: #FBBC05;
}

/* V - yellow */
#animated-name span.y {
  color: #34A853;
}

/* y - green */
#animated-name span.a2 {
  color: #4285F4;
}

/* a - blue */
#animated-name span.s2 {
  color: #EA4335;
}

/* s - red */

/* ========== SOCIAL LINKS ========== */
.social-links {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.social-links a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.social-links a:hover {
  color: var(--text-lighter);
  transform: translateY(-2px);
}

/* ========== FOOTER ========== */
footer {
  text-align: center;
  padding: 30px;
  background-color: var(--bg-light);
  margin-top: 60px;
  font-size: 0.9rem;
  color: var(--text-gray);
}

/* ========== MEDIA QUERIES ========== */
@media (max-width: 992px) {
  .book-cover {
  width: 140px;
  /* Slightly smaller to fit better */
  height: 200px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

  .main-container {
    flex-direction: column;
    gap: 30px;
  }

  .right-photo {
    position: static;
    order: -1;
    margin-bottom: 40px;
  }

  .right-photo img {
    width: 180px;
    height: 180px;
  }
}

@media (max-width: 768px) {
  body {
    margin: auto;
    align-items: center;
    align-content: center;
  }
  #bg-overlay {
  position:fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* 50% dark overlay */
  z-index: -1;
}
  html {
    font-size: 15px;
  }

  .nav-links {
    flex-wrap: wrap;
    gap: 10px;
    font-size: 14px;
  }

  .section {
    padding: 60px 15px;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  /* Project buttons responsive */
  .project-buttons {
    flex-direction: column;
    gap: 8px;
  }

  /* Threads responsive adjustments */
  .book-preview {
    flex-direction: column;
    gap: 15px;
  }

  .preview-quote {
    border-left: none;
    border-top: 2px solid var(--primary);
    padding-left: 0;
    padding-top: 15px;
  }

  .expand-indicator {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .nav-links {
    gap: 8px;
  }

  .section {
    padding: 50px 10px;
  }

  .btn {
    padding: 10px 18px;
    width: 100%;
  }

  /* Threads responsive adjustments */
  .book-preview {
    padding: 15px;
  }

  .book-meta {
    flex-direction: column;
    gap: 5px;
  }

  .quotes-container {
    padding: 0 15px;
  }

  .book-card.expanded .quotes-container {
    padding: 0 15px 15px 15px;
  }
}